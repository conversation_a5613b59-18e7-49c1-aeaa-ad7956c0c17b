# File: lambda_function/dispatcher.py

import json
from .intent_handlers import handle_get_weather, handle_book_flight, close

INTENT_HANDLERS = {
    'GetWeatherForecast': handle_get_weather,
    'BookFlight': handle_book_flight,
}

def lambda_handler(event, context):
    """
    Main dispatcher that routes the incoming request to the correct intent handler.
    """
    print(f"Received event: {json.dumps(event)}")

    intent_name = event['sessionState']['intent']['name']

    # Look up the handler in the dictionary and execute it
    handler = INTENT_HANDLERS.get(intent_name)
    if handler:
        return handler(event)
    else:
        # A fallback for any unexpected intents
        return close(event, 'Failed', f"Sorry, I don't know how to handle the '{intent_name}' intent.")