variable "aws_region" {
  description = "The AWS region to deploy resources to."
  type        = string
  default     = "us-east-1" # You can change this to your desired region
}

variable "openweathermap_api_key" {
  description = "API key for OpenWeatherMap."
  type        = string
  sensitive   = true

  validation {
    condition     = length(var.openweathermap_api_key) > 0
    error_message = "The OpenWeatherMap API key must not be empty."
  }
}
