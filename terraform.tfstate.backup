{"version": 4, "terraform_version": "1.10.4", "serial": 16, "lineage": "73298005-2921-0b38-9f09-6e3a277bf618", "outputs": {}, "resources": [{"mode": "data", "type": "archive_file", "name": "lambda_zip", "provider": "provider[\"registry.terraform.io/hashicorp/archive\"]", "instances": [{"schema_version": 0, "attributes": {"exclude_symlink_directories": null, "excludes": null, "id": "4a3ff7b00e72a3eb99e0728359bd4a4c00c52881", "output_base64sha256": "8ONPjU4/YGkopJtbzCbR8oUrMaIZuxGYicKNU4bAxKg=", "output_base64sha512": "4Z5rgQwoBcjAgCtnDzIsBiG1aulQmxH1cFLKikAKhSKIiJatALOBrAhgkZSBykItcok4f0pVtYAInKkoJE4iXw==", "output_file_mode": null, "output_md5": "2eb29538fcf69b5148fdde6951a4119a", "output_path": "./lambda_function.zip", "output_sha": "4a3ff7b00e72a3eb99e0728359bd4a4c00c52881", "output_sha256": "f0e34f8d4e3f606928a49b5bcc26d1f2852b31a219bb119889c28d5386c0c4a8", "output_sha512": "e19e6b810c2805c8c0802b670f322c0621b56ae9509b11f57052ca8a400a8522888896ad00b381ac0860919481ca422d7289387f4a55b580089ca928244e225f", "output_size": 1691, "source": [], "source_content": null, "source_content_filename": null, "source_dir": "./lambda_fuction", "source_file": null, "type": "zip"}, "sensitive_attributes": []}]}, {"mode": "managed", "type": "aws_iam_role", "name": "lambda_exec_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::043309365033:role/TravelBot-Lambda-Role-Terraform", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-24T16:28:48Z", "description": "", "force_detach_policies": false, "id": "TravelBot-Lambda-Role-Terraform", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"], "max_session_duration": 3600, "name": "TravelBot-Lambda-Role-Terraform", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAQUFLQO4UZKNZCEDKI"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role", "name": "lex_service_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::043309365033:role/TravelBot-Lex-Role-Terraform", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lexv2.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-24T16:28:48Z", "description": "", "force_detach_policies": false, "id": "TravelBot-Lex-Role-Terraform", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "TravelBot-Lex-Role-Terraform", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {}, "tags_all": {}, "unique_id": "AROAQUFLQO4URHPTRSFOC"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "lambda_logs", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "TravelBot-Lambda-Role-Terraform-20250624162848505500000001", "policy_arn": "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole", "role": "TravelBot-Lambda-Role-Terraform"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_iam_role.lambda_exec_role"]}]}, {"mode": "managed", "type": "aws_lambda_function", "name": "get_weather_function", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architectures": ["x86_64"], "arn": "arn:aws:lambda:us-east-1:043309365033:function:getWeatherForecastFunction-Terraform", "code_sha256": "8ONPjU4/YGkopJtbzCbR8oUrMaIZuxGYicKNU4bAxKg=", "code_signing_config_arn": "", "dead_letter_config": [], "description": "", "environment": [{"variables": {"API_KEY": ""}}], "ephemeral_storage": [{"size": 512}], "file_system_config": [], "filename": "./lambda_function.zip", "function_name": "getWeatherForecastFunction-Terraform", "handler": "get_weather.lambda_handler", "id": "getWeatherForecastFunction-Terraform", "image_config": [], "image_uri": "", "invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:043309365033:function:getWeatherForecastFunction-Terraform/invocations", "kms_key_arn": "", "last_modified": "2025-06-24T16:28:56.115+0000", "layers": [], "logging_config": [{"application_log_level": "", "log_format": "Text", "log_group": "/aws/lambda/getWeatherForecastFunction-Terraform", "system_log_level": ""}], "memory_size": 128, "package_type": "Zip", "publish": false, "qualified_arn": "arn:aws:lambda:us-east-1:043309365033:function:getWeatherForecastFunction-Terraform:$LATEST", "qualified_invoke_arn": "arn:aws:apigateway:us-east-1:lambda:path/2015-03-31/functions/arn:aws:lambda:us-east-1:043309365033:function:getWeatherForecastFunction-Terraform:$LATEST/invocations", "replace_security_groups_on_destroy": null, "replacement_security_group_ids": null, "reserved_concurrent_executions": -1, "role": "arn:aws:iam::043309365033:role/TravelBot-Lambda-Role-Terraform", "runtime": "python3.9", "s3_bucket": null, "s3_key": null, "s3_object_version": null, "signing_job_arn": "", "signing_profile_version_arn": "", "skip_destroy": false, "snap_start": [], "source_code_hash": "8ONPjU4/YGkopJtbzCbR8oUrMaIZuxGYicKNU4bAxKg=", "source_code_size": 1691, "tags": {}, "tags_all": {}, "timeout": 10, "timeouts": null, "tracing_config": [{"mode": "PassThrough"}], "version": "$LATEST", "vpc_config": []}, "sensitive_attributes": [[{"type": "get_attr", "value": "environment"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "variables"}, {"type": "index", "value": {"value": "API_KEY", "type": "string"}}]], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6NjAwMDAwMDAwMDAwLCJ1cGRhdGUiOjYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.lambda_exec_role", "data.archive_file.lambda_zip"]}]}, {"mode": "managed", "type": "aws_lexv2models_bot", "name": "travelbot", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:lex:us-east-1:043309365033:bot/OQRUTLIHY8", "data_privacy": [{"child_directed": false}], "description": null, "id": "OQRUTLIHY8", "idle_session_ttl_in_seconds": 300, "members": [], "name": "TravelBot-Terraform", "role_arn": "arn:aws:iam::043309365033:role/TravelBot-Lex-Role-Terraform", "tags": null, "tags_all": {}, "test_bot_alias_tags": null, "timeouts": null, "type": "Bot"}, "sensitive_attributes": [], "dependencies": ["aws_iam_role.lex_service_role"]}]}, {"mode": "managed", "type": "aws_lexv2models_bot_locale", "name": "en_us", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bot_id": "OQRUTLIHY8", "bot_version": "DRAFT", "description": null, "id": "en_US,OQRUTLIHY8,DRAFT", "locale_id": "en_US", "n_lu_intent_confidence_threshold": 0.4, "name": "English (US)", "timeouts": null, "voice_settings": []}, "sensitive_attributes": [], "dependencies": ["aws_iam_role.lex_service_role", "aws_lexv2models_bot.travelbot"]}]}, {"mode": "managed", "type": "aws_lexv2models_intent", "name": "get_weather", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bot_id": "OQRUTLIHY8", "bot_version": "DRAFT", "closing_setting": [], "confirmation_setting": [], "creation_date_time": "2025-06-24T16:29:11Z", "description": null, "dialog_code_hook": [], "fulfillment_code_hook": [{"active": null, "enabled": true, "fulfillment_updates_specification": [], "post_fulfillment_status_specification": []}], "id": "YXLVGUTQWL:OQRUTLIHY8:DRAFT:en_US", "initial_response_setting": [], "input_context": [], "intent_id": "YXLVGUTQWL", "kendra_configuration": [], "last_updated_date_time": "2025-06-24T16:35:46Z", "locale_id": "en_US", "name": "GetWeatherForecast", "output_context": [], "parent_intent_signature": null, "sample_utterance": [{"utterance": "what is the weather in {city}"}, {"utterance": "what is the weather for {city} on {date}"}, {"utterance": "forecast for {city}"}, {"utterance": "tell me the weather in {city}"}], "slot_priority": [], "timeouts": null}, "sensitive_attributes": [], "dependencies": ["aws_iam_role.lex_service_role", "aws_lexv2models_bot.travelbot", "aws_lexv2models_bot_locale.en_us"]}]}, {"mode": "managed", "type": "aws_lexv2models_slot", "name": "city_slot", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"status": "tainted", "schema_version": 0, "attributes": {"bot_id": "OQRUTLIHY8", "bot_version": "DRAFT", "description": null, "id": "OQRUTLIHY8,DRAFT,YXLVGUTQWL,en_US,YKH6NZTJBM", "intent_id": "YXLVGUTQWL", "locale_id": "en_US", "multiple_values_setting": [], "name": "city", "obfuscation_setting": [], "slot_id": "YKH6NZTJBM", "slot_type_id": "AMAZON.City", "sub_slot_setting": [], "timeouts": null, "value_elicitation_setting": [{"default_value_specification": [], "prompt_specification": [{"allow_interrupt": true, "max_retries": 2, "message_group": [{"message": [{"custom_payload": [], "image_response_card": [], "plain_text_message": [{"value": "For which city do you need the weather?"}], "ssml_message": []}], "variation": []}], "message_selection_strategy": "Random", "prompt_attempts_specification": [{"allow_interrupt": true, "allowed_input_types": [{"allow_audio_input": true, "allow_dtmf_input": true}], "audio_and_dtmf_input_specification": [{"audio_specification": [{"end_timeout_ms": 640, "max_length_ms": 15000}], "dtmf_specification": [{"deletion_character": "*", "end_character": "#", "end_timeout_ms": 5000, "max_length": 513}], "start_timeout_ms": 4000}], "map_block_key": "Initial", "text_input_specification": [{"start_timeout_ms": 30000}]}, {"allow_interrupt": true, "allowed_input_types": [{"allow_audio_input": true, "allow_dtmf_input": true}], "audio_and_dtmf_input_specification": [{"audio_specification": [{"end_timeout_ms": 640, "max_length_ms": 15000}], "dtmf_specification": [{"deletion_character": "*", "end_character": "#", "end_timeout_ms": 5000, "max_length": 513}], "start_timeout_ms": 4000}], "map_block_key": "Retry1", "text_input_specification": [{"start_timeout_ms": 30000}]}, {"allow_interrupt": true, "allowed_input_types": [{"allow_audio_input": true, "allow_dtmf_input": true}], "audio_and_dtmf_input_specification": [{"audio_specification": [{"end_timeout_ms": 640, "max_length_ms": 15000}], "dtmf_specification": [{"deletion_character": "*", "end_character": "#", "end_timeout_ms": 5000, "max_length": 513}], "start_timeout_ms": 4000}], "map_block_key": "Retry2", "text_input_specification": [{"start_timeout_ms": 30000}]}]}], "sample_utterance": [], "slot_constraint": "Required", "slot_resolution_setting": [], "wait_and_continue_specification": []}]}, "sensitive_attributes": [], "dependencies": ["aws_iam_role.lex_service_role", "aws_lexv2models_bot.travelbot", "aws_lexv2models_bot_locale.en_us", "aws_lexv2models_intent.get_weather"]}]}, {"mode": "managed", "type": "aws_lexv2models_slot", "name": "date_slot", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"status": "tainted", "schema_version": 0, "attributes": {"bot_id": "OQRUTLIHY8", "bot_version": "DRAFT", "description": null, "id": "OQRUTLIHY8,DRAFT,YXLVGUTQWL,en_US,DYJJHT49U6", "intent_id": "YXLVGUTQWL", "locale_id": "en_US", "multiple_values_setting": [], "name": "date", "obfuscation_setting": [], "slot_id": "DYJJHT49U6", "slot_type_id": "AMAZON.Date", "sub_slot_setting": [], "timeouts": null, "value_elicitation_setting": [{"default_value_specification": [], "prompt_specification": [{"allow_interrupt": true, "max_retries": 2, "message_group": [{"message": [{"custom_payload": [], "image_response_card": [], "plain_text_message": [{"value": "And for what date?"}], "ssml_message": []}], "variation": []}], "message_selection_strategy": "Random", "prompt_attempts_specification": [{"allow_interrupt": true, "allowed_input_types": [{"allow_audio_input": true, "allow_dtmf_input": true}], "audio_and_dtmf_input_specification": [{"audio_specification": [{"end_timeout_ms": 640, "max_length_ms": 15000}], "dtmf_specification": [{"deletion_character": "*", "end_character": "#", "end_timeout_ms": 5000, "max_length": 513}], "start_timeout_ms": 4000}], "map_block_key": "Initial", "text_input_specification": [{"start_timeout_ms": 30000}]}, {"allow_interrupt": true, "allowed_input_types": [{"allow_audio_input": true, "allow_dtmf_input": true}], "audio_and_dtmf_input_specification": [{"audio_specification": [{"end_timeout_ms": 640, "max_length_ms": 15000}], "dtmf_specification": [{"deletion_character": "*", "end_character": "#", "end_timeout_ms": 5000, "max_length": 513}], "start_timeout_ms": 4000}], "map_block_key": "Retry1", "text_input_specification": [{"start_timeout_ms": 30000}]}, {"allow_interrupt": true, "allowed_input_types": [{"allow_audio_input": true, "allow_dtmf_input": true}], "audio_and_dtmf_input_specification": [{"audio_specification": [{"end_timeout_ms": 640, "max_length_ms": 15000}], "dtmf_specification": [{"deletion_character": "*", "end_character": "#", "end_timeout_ms": 5000, "max_length": 513}], "start_timeout_ms": 4000}], "map_block_key": "Retry2", "text_input_specification": [{"start_timeout_ms": 30000}]}]}], "sample_utterance": [], "slot_constraint": "Optional", "slot_resolution_setting": [], "wait_and_continue_specification": []}]}, "sensitive_attributes": [], "dependencies": ["aws_iam_role.lex_service_role", "aws_lexv2models_bot.travelbot", "aws_lexv2models_bot_locale.en_us", "aws_lexv2models_intent.get_weather"]}]}], "check_results": null}