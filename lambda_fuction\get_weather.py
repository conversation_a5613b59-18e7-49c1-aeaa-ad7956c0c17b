# File: lambda_function/get_weather.py (Corrected Version)

import json
import os
import urllib3

def lambda_handler(event, context):
    """
    Main handler that Amazon Lex invokes. This version is robust against case-sensitivity in slot names.
    """
    try:
        # Get slots from the event, converting all keys to lowercase for consistency
        slots = {key.lower(): value for key, value in event['sessionState']['intent']['slots'].items()}
        
        # Access the city slot using the lowercase key
        city = slots['city']['value']['interpretedValue']
        
    except (KeyError, TypeError, AttributeError):
        print("Error: Could not extract 'city' slot from the event.")
        return close(event, 'Failed', 'I encountered an error. Please specify a city and try again.')

    API_KEY = os.environ.get('API_KEY')
    if not API_KEY:
        print("Error: API_KEY environment variable not set.")
        return close(event, 'Failed', 'The bot is not configured correctly. Please contact support.')

    http = urllib3.PoolManager()
    url = f"https://api.openweathermap.org/data/2.5/weather?q={city}&appid={API_KEY}&units=metric"

    try:
        response = http.request('GET', url)
        data = json.loads(response.data.decode('utf-8'))
        
        if data.get('cod') != 200:
            error_message = data.get('message', 'an unknown error')
            print(f"Error from API for city '{city}': {error_message}")
            text_response = f"I'm sorry, I couldn't find the weather for {city}. Please check the city name."
        else:
            weather_description = data['weather'][0]['description']
            temperature = round(data['main']['temp'])
            text_response = f"The current weather in {city} is {temperature}°C with {weather_description}."
            
        return close(event, 'Fulfilled', text_response)

    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return close(event, 'Failed', "I'm sorry, I ran into an issue getting the weather. Please try again.")

def close(event, fulfillment_state, message_content):
    """
    Constructs the final JSON response object for Amazon Lex.
    """
    intent_name = event.get('sessionState', {}).get('intent', {}).get('name', 'UnknownIntent')
    
    response = {
        'sessionState': {
            'dialogAction': { 'type': 'Close' },
            'intent': {
                'name': intent_name,
                'state': fulfillment_state
            }
        },
        'messages': [{'contentType': 'PlainText', 'content': message_content}]
    }
    return response
