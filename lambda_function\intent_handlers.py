# File: lambda_function/intent_handlers.py

import os
import json
import urllib3

def get_slot_value(event, slot_name):
    """Safely retrieves the interpreted value of a slot from a Lex event."""
    slots = event.get('sessionState', {}).get('intent', {}).get('slots', {})
    if not slots or slot_name not in slots or not slots[slot_name]:
        return None
    return slots[slot_name].get('value', {}).get('interpretedValue')

# --- GetWeatherForecast Logic ---
def handle_get_weather(event):
    """
    Handles the GetWeatherForecast intent by calling the OpenWeatherMap API.
    """
    city = get_slot_value(event, 'city')
    if not city:
        return close(event, 'Failed', 'I need a city to get the weather for.')

    API_KEY = os.environ.get('API_KEY')
    
    if not API_KEY:
        return close(event, 'Failed', 'API key is not configured correctly.')

    http = urllib3.PoolManager()
    url = f"https://api.openweathermap.org/data/2.5/weather?q={city}&appid={API_KEY}&units=metric"

    try:
        response = http.request('GET', url)
        data = json.loads(response.data.decode('utf-8'))
        
        if response.status != 200 or data.get('cod') != 200:
            print(f"API Error for city '{city}': {data.get('message', 'Unknown error')}")
            text_response = f"I couldn't find the weather for {city}. Please check the city name."
        else:
            weather_description = data['weather'][0]['description']
            temperature = round(data['main']['temp'])
            text_response = f"The current weather in {city} is {temperature}°C with {weather_description}."
            
        return close(event, 'Fulfilled', text_response)

    except Exception as e:
        print(f"Error during API call: {e}")
        return close(event, 'Failed', "Sorry, I ran into an issue getting the weather.")


# --- BookFlight Logic ---
def handle_book_flight(event):
    """
    Handles the BookFlight intent. This is a mock function.
    """
    departure_city = get_slot_value(event, 'departureCity') or 'not specified'
    destination_city = get_slot_value(event, 'destinationCity') or 'not specified'
    departure_date = get_slot_value(event, 'departureDate') or 'not specified'

    text_response = (f"Okay, I have the details to book a flight from {departure_city} "
                     f"to {destination_city} on {departure_date}. "
                     f"This feature is not yet fully implemented.")

    return close(event, 'Fulfilled', text_response)


# --- Helper Function ---
def close(event, fulfillment_state, message_content):
    """
    Constructs the final JSON response object for Amazon Lex.
    """
    response = {
        'sessionState': {
            'dialogAction': { 'type': 'Close' },
            'intent': {
                'name': event.get('sessionState', {}).get('intent', {}).get('name'),
                'state': fulfillment_state
            }
        },
        'messages': [{'contentType': 'PlainText', 'content': message_content}],
    }
    return response