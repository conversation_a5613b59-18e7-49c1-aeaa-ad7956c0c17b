# --- Provider and Backend Configuration ---
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Data source to get the current AWS Account ID, used for constructing ARNs.
data "aws_caller_identity" "current" {}

# --- IAM Roles and Permissions ---

# IAM role that the Lambda function will assume to get execution permissions.
resource "aws_iam_role" "lambda_exec_role" {
  name = "TravelBot-Lambda-Role-Terraform"
  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = { Service = "lambda.amazonaws.com" }
    }]
  })
}

# Attaches the basic Lambda execution policy to write logs to CloudWatch.
resource "aws_iam_role_policy_attachment" "lambda_logs" {
  role       = aws_iam_role.lambda_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# IAM role that the Amazon Lex service will assume.
resource "aws_iam_role" "lex_service_role" {
  name = "TravelBot-Lex-Role-Terraform"
  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = { Service = "lexv2.amazonaws.com" }
    }]
  })
}

# --- Lambda Function ---

# Zips the contents of the lambda_function directory for deployment.
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/lambda_function"
  output_path = "${path.module}/lambda_function.zip"
}

resource "aws_lambda_function" "dispatcher_function" {
  # Note: The function name has been updated for clarity.
  function_name    = "TravelBot-Dispatcher-Terraform"
  handler          = "dispatcher.lambda_handler"
  runtime          = "python3.9"
  role             = aws_iam_role.lambda_exec_role.arn
  filename         = data.archive_file.lambda_zip.output_path
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256
  timeout          = 10

  environment {
    variables = {
      API_KEY = var.openweathermap_api_key
    }
  }
}

# --- Amazon Lex V2 Bot ---

# Defines the core Lex bot resource.
resource "aws_lexv2models_bot" "travelbot" {
  name                      = "TravelBot-Terraform"
  role_arn                  = aws_iam_role.lex_service_role.arn
  idle_session_ttl_in_seconds = 300
  data_privacy {
    child_directed = false
  }
}

# Defines the locale (language and region) for the bot.
resource "aws_lexv2models_bot_locale" "en_us" {
  bot_id                          = aws_lexv2models_bot.travelbot.id
  bot_version                     = "DRAFT"
  locale_id                       = "en_US"
  nlu_intent_confidence_threshold = 0.40 # Corrected argument name for recent provider versions.
  voice_settings {
    voice_id = "Joanna"
  }
}

# Defines the "GetWeatherForecast" intent.
resource "aws_lexv2models_intent" "get_weather" {
  name        = "GetWeatherForecast"
  bot_id      = aws_lexv2models_bot.travelbot.id
  bot_version = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id   = aws_lexv2models_bot_locale.en_us.locale_id

  sample_utterances = [
    { utterance = "what is the weather in {city}" },
    { utterance = "what is the weather for {city} on {date}" },
    { utterance = "forecast for {city}" }
  ]

  fulfillment_code_hook {
    enabled = true
  }

  slot {
    name         = "city"
    slot_type_id = "AMAZON.City"
    value_elicitation_setting {
      prompt_specification {
        max_retries = 2
        message_group { message { plain_text_message { value = "For which city?" } } }
      }
      slot_constraint = "Required"
    }
  }

  slot {
    name         = "date"
    slot_type_id = "AMAZON.Date"
    value_elicitation_setting {
      prompt_specification {
        max_retries = 2
        message_group { message { plain_text_message { value = "And for what date?" } } }
      }
      slot_constraint = "Optional"
    }
  }
}

# Defines the "BookFlight" intent.
resource "aws_lexv2models_intent" "book_flight" {
  name        = "BookFlight"
  bot_id      = aws_lexv2models_bot.travelbot.id
  bot_version = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id   = aws_lexv2models_bot_locale.en_us.locale_id

  sample_utterances = [
    { utterance = "book a flight" },
    { utterance = "I want to book a flight to {destinationCity}" },
    { utterance = "find flights from {departureCity} to {destinationCity}" }
  ]

  fulfillment_code_hook {
    enabled = true
  }

  slot {
    name         = "departureCity"
    slot_type_id = "AMAZON.City"
    value_elicitation_setting {
      prompt_specification {
        max_retries = 2
        message_group { message { plain_text_message { value = "What city are you departing from?" } } }
      }
      slot_constraint = "Required"
    }
  }

  slot {
    name         = "destinationCity"
    slot_type_id = "AMAZON.City"
    value_elicitation_setting {
      prompt_specification {
        max_retries = 2
        message_group { message { plain_text_message { value = "What is your destination city?" } } }
      }
      slot_constraint = "Required"
    }
  }

  slot {
    name         = "departureDate"
    slot_type_id = "AMAZON.Date"
    value_elicitation_setting {
      prompt_specification {
        max_retries = 2
        message_group { message { plain_text_message { value = "What date would you like to depart?" } } }
      }
      slot_constraint = "Required"
    }
  }
}

# --- Lambda Permission ---
# Grants the Amazon Lex service permission to invoke the Lambda function.
resource "aws_lambda_permission" "lex_invoke" {
  statement_id  = "AllowLexToCallLambda"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.dispatcher_function.function_name
  principal     = "lexv2.amazonaws.com"
  
  # This wildcard ARN allows any alias of this specific bot to invoke the function,
  # which is ideal for development as it includes the auto-created TestBotAlias.
  source_arn    = "arn:aws:lex:${var.aws_region}:${data.aws_caller_identity.current.account_id}:bot-alias/${aws_lexv2models_bot.travelbot.id}/*"
}
