
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# --- IAM Roles and Permissions ---

# 1. IAM Role for the Lambda function
resource "aws_iam_role" "lambda_exec_role" {
  name = "TravelBot-Lambda-Role-Terraform"
  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = { Service = "lambda.amazonaws.com" }
    }]
  })
}

# 2. Attach basic execution policy for CloudWatch logging
resource "aws_iam_role_policy_attachment" "lambda_logs" {
  role       = aws_iam_role.lambda_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# 3. IAM Role for the Lex bot
resource "aws_iam_role" "lex_service_role" {
  name = "TravelBot-Lex-Role-Terraform"
  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = { Service = "lexv2.amazonaws.com" }
    }]
  })
}


# --- Lambda Function ---

# 1. Package the Python code into a deployable .zip file
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/lambda_fuction"
  output_path = "${path.module}/lambda_function.zip"
}

# 2. Create the Lambda function resource
resource "aws_lambda_function" "get_weather_function" {
  function_name    = "getWeatherForecastFunction-Terraform"
  handler          = "get_weather.lambda_handler"
  runtime          = "python3.11"
  role             = aws_iam_role.lambda_exec_role.arn
  filename         = data.archive_file.lambda_zip.output_path
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256
  timeout          = 10

  environment {
    variables = {
      API_KEY = var.openweathermap_api_key
    }
  }
}


# --- Amazon Lex V2 Bot ---

# 1. Define the Bot resource
resource "aws_lexv2models_bot" "travelbot" {
  name                      = "TravelBot-Terraform"
  role_arn                  = aws_iam_role.lex_service_role.arn
  idle_session_ttl_in_seconds = 300

  data_privacy {
    child_directed = false
  }
}

# 2. Define the English (US) Locale for the Bot
resource "aws_lexv2models_bot_locale" "en_us" {
  bot_id                            = aws_lexv2models_bot.travelbot.id
  bot_version                       = "DRAFT"
  locale_id                         = "en_US"
  n_lu_intent_confidence_threshold  = 0.40
}

# 3. Define the 'GetWeatherForecast' Intent
# NOTE: Slots are now correctly defined *inside* this resource block.
resource "aws_lexv2models_intent" "get_weather" {
  name        = "GetWeatherForecast"
  bot_id      = aws_lexv2models_bot.travelbot.id
  bot_version = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id   = aws_lexv2models_bot_locale.en_us.locale_id

  # More varied utterances to improve NLU training
  sample_utterance {
    utterance = "what is the weather in {city}"
  }
  
  sample_utterance {
    utterance = "what is the weather for {city} on {date}"
  }
  
  sample_utterance {
    utterance = "forecast for {city}"
  }
  
  sample_utterance {
    utterance = "tell me the weather in {city}"
  }
  
  sample_utterance {
    utterance = "how is the weather in {city} {date}"
  }
  
  sample_utterance {
    utterance = "{city} weather"
  }
  
  sample_utterance {
    utterance = "{city} weather for {date}"
  }

  # Explicitly enable the Lambda hook for fulfillment for this intent
  fulfillment_code_hook {
    enabled = true
  }



  # This dependency ensures the locale is created before this intent
  depends_on = [aws_lexv2models_bot_locale.en_us]
}

# 3a. Define the 'city' slot for the GetWeatherForecast intent
resource "aws_lexv2models_slot" "city_slot" {
  name         = "city"
  bot_id       = aws_lexv2models_bot.travelbot.id
  bot_version  = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id    = aws_lexv2models_bot_locale.en_us.locale_id
  intent_id    = aws_lexv2models_intent.get_weather.intent_id
  slot_type_id = "AMAZON.City"
  
  value_elicitation_setting {
    slot_constraint = "Required"
    prompt_specification {
      max_retries = 2
      message_group {
        message {
          plain_text_message {
            value = "For which city do you need the weather?"
          }
        }
      }
    }
  }
}

# 3b. Define the 'date' slot for the GetWeatherForecast intent
resource "aws_lexv2models_slot" "date_slot" {
  name         = "date"
  bot_id       = aws_lexv2models_bot.travelbot.id
  bot_version  = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id    = aws_lexv2models_bot_locale.en_us.locale_id
  intent_id    = aws_lexv2models_intent.get_weather.intent_id
  slot_type_id = "AMAZON.Date"

  value_elicitation_setting {
    slot_constraint = "Optional"
    prompt_specification {
      max_retries = 2
      message_group {
        message {
          plain_text_message {
            value = "And for what date?"
          }
        }
      }
    }
  }
}

# 4. Create a Bot Version from the DRAFT
resource "aws_lexv2models_bot_version" "v1" {
  bot_id      = aws_lexv2models_bot.travelbot.id
  locale_specification = {
    (aws_lexv2models_bot_locale.en_us.locale_id) = {
      source_bot_version = aws_lexv2models_bot_locale.en_us.bot_version
    }
  }
  
  # This dependency waits until the intent and slots are fully configured
  depends_on = [
    aws_lexv2models_intent.get_weather,
    aws_lexv2models_slot.city_slot,
    aws_lexv2models_slot.date_slot
  ]
}

# 5. Create the Bot Alias and LINK THE LAMBDA
resource "aws_lexv2models_bot_alias" "test_alias" {
  name        = "TestBotAlias"
  bot_id      = aws_lexv2models_bot.travelbot.id
  bot_version = aws_lexv2models_bot_version.v1.bot_version

  # Link the Lambda function for fulfillment at the alias level
  bot_alias_locale_settings {
    locale_id = "en_US"
    enabled   = true
    code_hook_specification {
      lambda_code_hook {
        code_hook_interface_version = "1.0"
        lambda_arn                  = aws_lambda_function.get_weather_function.arn
      }
    }
  }
}

# --- Lambda Permission ---
# Grant Lex permission to invoke the Lambda function from the alias
resource "aws_lambda_permission" "lex_invoke" {
  statement_id  = "AllowLexToCallLambda"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.get_weather_function.function_name
  principal     = "lexv2.amazonaws.com"
  # The source ARN must reference the alias that will be invoking the function
  source_arn    = aws_lexv2models_bot_alias.test_alias.arn
}
