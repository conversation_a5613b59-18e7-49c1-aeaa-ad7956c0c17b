# File: main.tf (Final Corrected Version)

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  region = var.aws_region
}

# Data source to get the current AWS Account ID for ARNs.
data "aws_caller_identity" "current" {}

# --- IAM Roles and Permissions ---

# IAM role that the Lambda function will assume to get execution permissions.
resource "aws_iam_role" "lambda_exec_role" {
  name = "TravelBot-Lambda-Role-Terraform"
  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = { Service = "lambda.amazonaws.com" }
    }]
  })
}

# Attaches the basic Lambda execution policy to write logs to CloudWatch.
resource "aws_iam_role_policy_attachment" "lambda_logs" {
  role       = aws_iam_role.lambda_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

# IAM role that the Amazon Lex service will assume.
resource "aws_iam_role" "lex_service_role" {
  name = "TravelBot-Lex-Role-Terraform"
  assume_role_policy = jsonencode({
    Version   = "2012-10-17",
    Statement = [{
      Action    = "sts:AssumeRole",
      Effect    = "Allow",
      Principal = { Service = "lexv2.amazonaws.com" }
    }]
  })
}

# --- Lambda Function ---

# Zips the contents of the lambda_function directory for deployment.
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/lambda_function"
  output_path = "${path.module}/lambda_function.zip"
}

resource "aws_lambda_function" "dispatcher_function" {
  function_name    = "TravelBot-Dispatcher-Terraform"
  handler          = "dispatcher.lambda_handler"
  runtime          = "python3.9"
  role             = aws_iam_role.lambda_exec_role.arn
  filename         = data.archive_file.lambda_zip.output_path
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256
  timeout          = 10

  environment {
    variables = {
      API_KEY = var.openweathermap_api_key
    }
  }
}

# --- Amazon Lex V2 Bot ---

# Defines the core Lex bot resource.
resource "aws_lexv2models_bot" "travelbot" {
  name                      = "TravelBot-Terraform"
  role_arn                  = aws_iam_role.lex_service_role.arn
  idle_session_ttl_in_seconds = 300
  # CORRECTED: The 'data_privacy' block should not have an equals sign.
  data_privacy {
    child_directed = false
  }
}

# Defines the locale (language and region) for the bot.
resource "aws_lexv2models_bot_locale" "en_us" {
  bot_id                      = aws_lexv2models_bot.travelbot.id
  bot_version                 = "DRAFT"
  locale_id                   = "en_US"
  n_lu_intent_confidence_threshold = 0.40
  voice_settings {
    voice_id = "Joanna"
  }
}

# --- Intents and their Slots (Correct Separate Resource Structure) ---

# GetWeatherForecast Intent Definition
resource "aws_lexv2models_intent" "get_weather" {
  name        = "GetWeatherForecast"
  bot_id      = aws_lexv2models_bot.travelbot.id
  bot_version = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id   = aws_lexv2models_bot_locale.en_us.locale_id

  sample_utterance { utterance = "what is the weather in {city}" }
  sample_utterance { utterance = "what is the weather for {city} on {date}" }
  sample_utterance { utterance = "forecast for {city}" }

  fulfillment_code_hook {
    enabled = true
  }

  depends_on = [aws_lexv2models_bot_locale.en_us]
}

# Slots for GetWeatherForecast
resource "aws_lexv2models_slot" "weather_city_slot" {
  name         = "city"
  bot_id       = aws_lexv2models_intent.get_weather.bot_id
  bot_version  = aws_lexv2models_intent.get_weather.bot_version
  locale_id    = aws_lexv2models_intent.get_weather.locale_id
  intent_id    = aws_lexv2models_intent.get_weather.intent_id
  slot_type_id = "AMAZON.City"

  value_elicitation_setting {
    slot_constraint = "Required"
    prompt_specification {
      max_retries = 2
      message_group {
        message {
          plain_text_message {
            value = "For which city do you need the weather?"
          }
        }
      }
    }
  }
}

resource "aws_lexv2models_slot" "weather_date_slot" {
  name         = "date"
  bot_id       = aws_lexv2models_intent.get_weather.bot_id
  bot_version  = aws_lexv2models_intent.get_weather.bot_version
  locale_id    = aws_lexv2models_intent.get_weather.locale_id
  intent_id    = aws_lexv2models_intent.get_weather.intent_id
  slot_type_id = "AMAZON.Date"

  value_elicitation_setting {
    slot_constraint = "Optional"
    prompt_specification {
      max_retries = 2
      message_group {
        message {
          plain_text_message {
            value = "And for what date?"
          }
        }
      }
    }
  }
}

# BookFlight Intent Definition
resource "aws_lexv2models_intent" "book_flight" {
  name        = "BookFlight"
  bot_id      = aws_lexv2models_bot.travelbot.id
  bot_version = aws_lexv2models_bot_locale.en_us.bot_version
  locale_id   = aws_lexv2models_bot_locale.en_us.locale_id

  sample_utterance { utterance = "book a flight" }
  sample_utterance { utterance = "I want to book a flight to {destinationCity}" }
  sample_utterance { utterance = "find flights from {departureCity} to {destinationCity}" }

  fulfillment_code_hook {
    enabled = true
  }

  depends_on = [aws_lexv2models_bot_locale.en_us]
}

# Slots for BookFlight
resource "aws_lexv2models_slot" "flight_dep_city_slot" {
  name         = "departureCity"
  bot_id       = aws_lexv2models_intent.book_flight.bot_id
  bot_version  = aws_lexv2models_intent.book_flight.bot_version
  locale_id    = aws_lexv2models_intent.book_flight.locale_id
  intent_id    = aws_lexv2models_intent.book_flight.intent_id
  slot_type_id = "AMAZON.City"

  value_elicitation_setting {
    slot_constraint = "Required"
    prompt_specification {
      max_retries = 2
      message_group {
        message {
          plain_text_message {
            value = "What city are you departing from?"
          }
        }
      }
    }
  }
}

resource "aws_lexv2models_slot" "flight_dest_city_slot" {
  name         = "destinationCity"
  bot_id       = aws_lexv2models_intent.book_flight.bot_id
  bot_version  = aws_lexv2models_intent.book_flight.bot_version
  locale_id    = aws_lexv2models_intent.book_flight.locale_id
  intent_id    = aws_lexv2models_intent.book_flight.intent_id
  slot_type_id = "AMAZON.City"

  value_elicitation_setting {
    slot_constraint = "Required"
    prompt_specification {
      max_retries = 2
      message_group {
        message {
          plain_text_message {
            value = "What is your destination city?"
          }
        }
      }
    }
  }
}

resource "aws_lexv2models_slot" "flight_dep_date_slot" {
  name         = "departureDate"
  bot_id       = aws_lexv2models_intent.book_flight.bot_id
  bot_version  = aws_lexv2models_intent.book_flight.bot_version
  locale_id    = aws_lexv2models_intent.book_flight.locale_id
  intent_id    = aws_lexv2models_intent.book_flight.intent_id
  slot_type_id = "AMAZON.Date"

  value_elicitation_setting {
    slot_constraint = "Required"
    prompt_specification {
      max_retries = 2
      message_group {
        message {
          plain_text_message {
            value = "What date would you like to depart?"
          }
        }
      }
    }
  }
}
